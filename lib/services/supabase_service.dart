import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();

  SupabaseService._();

  // Supabase configuration - these should match your backend/.env file
  static const String supabaseUrl = 'http://localhost:8000';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

  SupabaseClient get client => Supabase.instance.client;

  // Static getter for client access
  static SupabaseClient get supabaseClient => Supabase.instance.client;

  static Future<void> initialize() async {
    try {
      developer.log('SupabaseService: Initializing with URL: $supabaseUrl');
      if (kDebugMode) {
        print('SupabaseService: Initializing Supabase...');
        print('SupabaseService: URL: $supabaseUrl');
      }

      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
        debug: true, // Enable debug mode for development
      );

      developer.log('SupabaseService: Successfully initialized');
      if (kDebugMode) {
        print('SupabaseService: Successfully initialized');
      }
    } catch (e, stackTrace) {
      developer.log(
        'SupabaseService: Failed to initialize',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('SupabaseService: INITIALIZATION ERROR: $e');
        print('SupabaseService: INITIALIZATION STACK TRACE: $stackTrace');
      }
      rethrow;
    }
  }

  // Get current user
  User? get currentUser => client.auth.currentUser;

  // Get current session
  Session? get currentSession => client.auth.currentSession;

  // Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  // Get auth stream for listening to auth state changes
  Stream<AuthState> get authStateChanges => client.auth.onAuthStateChange;

  // Test database connection
  Future<bool> testConnection() async {
    try {
      developer.log('SupabaseService: Testing database connection...');
      if (kDebugMode) {
        print('SupabaseService: Testing database connection...');
      }

      // Try a simple query to test the connection
      final response = await client.from('posts').select('count').limit(1);

      developer.log(
        'SupabaseService: Connection test successful - Response: $response',
      );
      if (kDebugMode) {
        print(
          'SupabaseService: Connection test successful - Response: $response',
        );
      }
      return true;
    } catch (e, stackTrace) {
      developer.log(
        'SupabaseService: Connection test failed',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('SupabaseService: CONNECTION TEST FAILED: $e');
        print('SupabaseService: CONNECTION TEST STACK TRACE: $stackTrace');
      }
      return false;
    }
  }
}
